using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time VWAP monitoring service implementation
/// Calculates rolling real-time VWAP using tick/aggregate data with Redis caching
/// Enables entries only above VWAP in trending market regimes
/// </summary>
public sealed class VWAPMonitorService : IVWAPMonitorService, IDisposable
{
    private readonly ITickStreamService _tickStreamService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketRegimeService _marketRegimeService;
    private readonly ILogger<VWAPMonitorService> _logger;
    
    private readonly ConcurrentDictionary<string, VWAPCalculator> _vwapCalculators = new();
    private readonly ConcurrentDictionary<string, VWAPTrend> _previousTrends = new();
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();
    
    private VWAPMonitorConfig _config;
    private VWAPMonitorStatus _status = VWAPMonitorStatus.Stopped;
    private bool _disposed;
    
    // Redis key patterns
    private const string VWAPKeyPattern = "vwap:{0}";
    private const string VWAPHistoryKeyPattern = "vwap:history:{0}";
    
    public event EventHandler<VWAPUpdateEventArgs>? VWAPUpdated;
    public event EventHandler<VWAPCrossEventArgs>? VWAPCrossed;
    
    public VWAPMonitorService(
        ITickStreamService tickStreamService,
        IOptimizedRedisConnectionService redisService,
        IMarketRegimeService marketRegimeService,
        IConfiguration configuration,
        ILogger<VWAPMonitorService> logger)
    {
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _marketRegimeService = marketRegimeService ?? throw new ArgumentNullException(nameof(marketRegimeService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Load configuration
        _config = LoadConfiguration(configuration);
        
        // Subscribe to tick stream events
        _tickStreamService.TradeReceived += OnTradeReceived;
        _tickStreamService.AggregateReceived += OnAggregateReceived;
        
        _logger.LogInformation("VWAPMonitorService initialized with {RollingMinutes}min rolling window", 
            _config.RollingMinutes);
    }
    
    public async Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(VWAPMonitorService));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        try
        {
            _status = VWAPMonitorStatus.Starting;
            _logger.LogInformation("Starting VWAP monitoring for {Count} symbols", symbolList.Count);
            
            // Initialize VWAP calculators for each symbol
            foreach (var symbol in symbolList)
            {
                var calculator = new VWAPCalculator(symbol, _config, _logger);
                _vwapCalculators.TryAdd(symbol, calculator);
                _previousTrends.TryAdd(symbol, VWAPTrend.Unknown);
                
                // Load cached VWAP data if available
                await LoadCachedVWAPAsync(symbol, calculator);
            }
            
            // Update monitored symbols
            lock (_symbolsLock)
            {
                foreach (var symbol in symbolList)
                {
                    _monitoredSymbols.Add(symbol);
                }
            }
            
            // Subscribe to tick stream for these symbols
            await _tickStreamService.SubscribeAsync(symbolList, TickDataTypes.Trades | TickDataTypes.Aggregates, cancellationToken);
            
            _status = VWAPMonitorStatus.Active;
            _logger.LogInformation("VWAP monitoring started successfully for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = VWAPMonitorStatus.Error;
            _logger.LogError(ex, "Error starting VWAP monitoring");
            throw;
        }
    }
    
    public Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        try
        {
            _logger.LogInformation("Stopping VWAP monitoring");
            
            // Clear monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
            
            // Clear calculators
            _vwapCalculators.Clear();
            _previousTrends.Clear();
            
            _status = VWAPMonitorStatus.Stopped;
            _logger.LogInformation("VWAP monitoring stopped");
        }
        catch (Exception ex)
        {
            _status = VWAPMonitorStatus.Error;
            _logger.LogError(ex, "Error stopping VWAP monitoring");
            throw;
        }

        return Task.CompletedTask;
    }
    
    public async Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(VWAPMonitorService));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        var newSymbols = new List<string>();
        
        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Add(symbol))
                {
                    newSymbols.Add(symbol);
                }
            }
        }
        
        if (newSymbols.Any())
        {
            foreach (var symbol in newSymbols)
            {
                var calculator = new VWAPCalculator(symbol, _config, _logger);
                _vwapCalculators.TryAdd(symbol, calculator);
                _previousTrends.TryAdd(symbol, VWAPTrend.Unknown);
                
                await LoadCachedVWAPAsync(symbol, calculator);
            }
            
            if (_status == VWAPMonitorStatus.Active)
            {
                await _tickStreamService.SubscribeAsync(newSymbols, TickDataTypes.Trades | TickDataTypes.Aggregates, cancellationToken);
            }
            
            _logger.LogInformation("Added {Count} new symbols to VWAP monitoring", newSymbols.Count);
        }
    }
    
    public Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return Task.CompletedTask;

        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return Task.CompletedTask;

        var removedSymbols = new List<string>();

        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Remove(symbol))
                {
                    removedSymbols.Add(symbol);
                }
            }
        }

        foreach (var symbol in removedSymbols)
        {
            _vwapCalculators.TryRemove(symbol, out _);
            _previousTrends.TryRemove(symbol, out _);
        }

        if (removedSymbols.Any())
        {
            _logger.LogInformation("Removed {Count} symbols from VWAP monitoring", removedSymbols.Count);
        }

        return Task.CompletedTask;
    }
    
    public async Task<VWAPData?> GetCurrentVWAPAsync(string symbol)
    {
        if (_disposed || !_vwapCalculators.TryGetValue(symbol, out var calculator))
            return null;
            
        try
        {
            // Try Redis cache first
            var database = await _redisService.GetDatabaseAsync();
            var cacheKey = string.Format(VWAPKeyPattern, symbol);
            var cachedData = await database.StringGetAsync(cacheKey);
            
            if (cachedData.HasValue)
            {
                var vwapData = JsonSerializer.Deserialize<VWAPData>(cachedData!);
                if (vwapData != null && DateTime.UtcNow - vwapData.Timestamp < _config.CacheExpiry)
                {
                    return vwapData;
                }
            }
            
            // Calculate current VWAP
            return calculator.GetCurrentVWAP();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current VWAP for {Symbol}", symbol);
            return null;
        }
    }
    
    public async Task<IEnumerable<VWAPData>> GetVWAPHistoryAsync(string symbol, int minutes = 60)
    {
        if (_disposed)
            return Enumerable.Empty<VWAPData>();
            
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var historyKey = string.Format(VWAPHistoryKeyPattern, symbol);
            var historyData = await database.ListRangeAsync(historyKey, 0, minutes - 1);
            
            var vwapHistory = new List<VWAPData>();
            foreach (var item in historyData)
            {
                if (item.HasValue)
                {
                    var vwapData = JsonSerializer.Deserialize<VWAPData>(item!);
                    if (vwapData != null)
                    {
                        vwapHistory.Add(vwapData);
                    }
                }
            }
            
            return vwapHistory.OrderByDescending(v => v.Timestamp);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting VWAP history for {Symbol}", symbol);
            return Enumerable.Empty<VWAPData>();
        }
    }
    
    public async Task<bool> IsPriceAboveVWAPAsync(string symbol, decimal currentPrice)
    {
        if (_config.RequireTrendingRegime)
        {
            var regime = await _marketRegimeService.GetCachedRegimeAsync();
            if (regime != MarketRegime.TrendingUp)
            {
                return false;
            }
        }
        
        var vwapData = await GetCurrentVWAPAsync(symbol);
        return vwapData != null && currentPrice > vwapData.VWAP;
    }
    
    public async Task<decimal?> GetVWAPDeviationAsync(string symbol, decimal currentPrice)
    {
        var vwapData = await GetCurrentVWAPAsync(symbol);
        if (vwapData == null || vwapData.VWAP == 0)
            return null;
            
        return (currentPrice - vwapData.VWAP) / vwapData.VWAP * 100;
    }
    
    public IEnumerable<string> GetMonitoredSymbols()
    {
        lock (_symbolsLock)
        {
            return _monitoredSymbols.ToList();
        }
    }
    
    public VWAPMonitorStatus GetStatus() => _status;
    
    public async Task UpdateParametersAsync(VWAPMonitorConfig config)
    {
        _config = config;
        
        // Update all calculators with new config
        foreach (var calculator in _vwapCalculators.Values)
        {
            calculator.UpdateConfig(config);
        }
        
        _logger.LogInformation("VWAP monitor configuration updated");
    }
    
    private VWAPMonitorConfig LoadConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("VWAPMonitor");
        
        return new VWAPMonitorConfig(
            RollingMinutes: section.GetValue("RollingMinutes", 30),
            MinTradesRequired: section.GetValue("MinTradesRequired", 10),
            MinVolumeThreshold: section.GetValue("MinVolumeThreshold", 1000m),
            RequireTrendingRegime: section.GetValue("RequireTrendingRegime", true),
            DeviationAlertThreshold: section.GetValue("DeviationAlertThreshold", 2.0m),
            CacheExpiry: TimeSpan.FromMinutes(section.GetValue("CacheExpiryMinutes", 5))
        );
    }
    
    private async Task LoadCachedVWAPAsync(string symbol, VWAPCalculator calculator)
    {
        try
        {
            var vwapData = await GetCurrentVWAPAsync(symbol);
            if (vwapData != null)
            {
                calculator.InitializeFromCache(vwapData);
                _logger.LogDebug("Loaded cached VWAP data for {Symbol}", symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error loading cached VWAP for {Symbol}", symbol);
        }
    }
    
    private async void OnTradeReceived(object? sender, TradeTickEventArgs e)
    {
        if (_disposed || !_vwapCalculators.TryGetValue(e.TradeTick.Symbol, out var calculator))
            return;

        try
        {
            // Update VWAP with trade data
            var vwapData = calculator.UpdateWithTrade(e.TradeTick.Price, e.TradeTick.Size, e.TradeTick.Timestamp);

            if (vwapData != null)
            {
                await ProcessVWAPUpdate(vwapData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade tick for VWAP calculation: {Symbol}", e.TradeTick.Symbol);
        }
    }

    private async void OnAggregateReceived(object? sender, AggregateTickEventArgs e)
    {
        if (_disposed || !_vwapCalculators.TryGetValue(e.AggregateTick.Symbol, out var calculator))
            return;

        try
        {
            // Update VWAP with aggregate data (use VWAP from aggregate if available)
            var vwapData = calculator.UpdateWithAggregate(e.AggregateTick);

            if (vwapData != null)
            {
                await ProcessVWAPUpdate(vwapData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing aggregate tick for VWAP calculation: {Symbol}", e.AggregateTick.Symbol);
        }
    }

    private async Task ProcessVWAPUpdate(VWAPData vwapData)
    {
        try
        {
            // Cache in Redis
            await CacheVWAPDataAsync(vwapData);

            // Check for trend changes
            var previousTrend = _previousTrends.GetValueOrDefault(vwapData.Symbol, VWAPTrend.Unknown);
            if (vwapData.Trend != previousTrend && previousTrend != VWAPTrend.Unknown)
            {
                _previousTrends[vwapData.Symbol] = vwapData.Trend;

                // Fire cross event
                VWAPCrossed?.Invoke(this, new VWAPCrossEventArgs
                {
                    Symbol = vwapData.Symbol,
                    Price = vwapData.CurrentPrice,
                    VWAP = vwapData.VWAP,
                    NewTrend = vwapData.Trend,
                    PreviousTrend = previousTrend,
                    Timestamp = vwapData.Timestamp
                });

                _logger.LogInformation("VWAP cross detected for {Symbol}: {PreviousTrend} -> {NewTrend} at {Price:F2} (VWAP: {VWAP:F2})",
                    vwapData.Symbol, previousTrend, vwapData.Trend, vwapData.CurrentPrice, vwapData.VWAP);
            }
            else
            {
                _previousTrends[vwapData.Symbol] = vwapData.Trend;
            }

            // Fire update event
            VWAPUpdated?.Invoke(this, new VWAPUpdateEventArgs { VWAPData = vwapData });

            // Check for deviation alerts
            if (Math.Abs(vwapData.DeviationPercent) >= _config.DeviationAlertThreshold)
            {
                _logger.LogInformation("VWAP deviation alert for {Symbol}: {Deviation:F2}% (threshold: {Threshold:F2}%)",
                    vwapData.Symbol, vwapData.DeviationPercent, _config.DeviationAlertThreshold);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing VWAP update for {Symbol}", vwapData.Symbol);
        }
    }

    private async Task CacheVWAPDataAsync(VWAPData vwapData)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(vwapData);

            // Cache current VWAP
            var currentKey = string.Format(VWAPKeyPattern, vwapData.Symbol);
            await database.StringSetAsync(currentKey, json, _config.CacheExpiry);

            // Add to history
            var historyKey = string.Format(VWAPHistoryKeyPattern, vwapData.Symbol);
            await database.ListLeftPushAsync(historyKey, json);
            await database.ListTrimAsync(historyKey, 0, 1440 - 1); // Keep 24 hours of minute data
            await database.KeyExpireAsync(historyKey, TimeSpan.FromDays(1));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching VWAP data for {Symbol}", vwapData.Symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _tickStreamService.TradeReceived -= OnTradeReceived;
            _tickStreamService.AggregateReceived -= OnAggregateReceived;

            _vwapCalculators.Clear();
            _previousTrends.Clear();

            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing VWAPMonitorService");
        }
    }
}
