using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using SmaTrendFollower.Services.ErrorHandling;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services.ErrorHandling;

public sealed class ErrorHandlerTests
{
    private readonly Mock<ILogger<ErrorHandler>> _mockLogger;
    private readonly ErrorHandlerOptions _options;
    private readonly ErrorHandler _errorHandler;

    public ErrorHandlerTests()
    {
        _mockLogger = new Mock<ILogger<ErrorHandler>>();
        _options = new ErrorHandlerOptions
        {
            MaxRetryAttempts = 3,
            BaseDelayMs = 100,
            MaxDelayMs = 5000,
            UseJitter = false, // Disable for predictable tests
            EnableCircuitBreaker = true,
            CircuitBreakerThreshold = 3,
            CircuitBreakerTimeoutMinutes = 1
        };
        _errorHandler = new ErrorHandler(_mockLogger.Object, Options.Create(_options));
    }

    [Fact]
    public async Task HandleErrorAsync_WithTradingException_ShouldReturnAppropriateStrategy()
    {
        // Arrange
        var exception = new MarketDataException("Test market data error", "AAPL", "TestProvider", isRetriable: true);
        var context = new ErrorContext
        {
            OperationName = "TestOperation",
            ServiceName = "TestService"
        };

        // Act
        var result = await _errorHandler.HandleErrorAsync(exception, context);

        // Assert
        result.Should().NotBeNull();
        result.IsHandled.Should().BeTrue();
        result.Strategy.Should().Be(RecoveryStrategy.Retry);
        result.ShouldRetry.Should().BeTrue();
        result.RetryDelay.Should().BeGreaterThan(TimeSpan.Zero);
    }

    [Fact]
    public async Task HandleErrorAsync_WithNonRetriableException_ShouldNotRetry()
    {
        // Arrange
        var exception = new TradeExecutionException("Trade failed", "AAPL", 100, "Market", isRetriable: false);
        var context = new ErrorContext
        {
            OperationName = "ExecuteTrade",
            ServiceName = "TradeExecutor"
        };

        // Act
        var result = await _errorHandler.HandleErrorAsync(exception, context);

        // Assert
        result.Should().NotBeNull();
        result.IsHandled.Should().BeTrue();
        result.Strategy.Should().Be(RecoveryStrategy.Stop);
        result.ShouldRetry.Should().BeFalse();
    }

    [Fact]
    public async Task HandleErrorAsync_WithHttpRequestException_ShouldRetry()
    {
        // Arrange
        var exception = new HttpRequestException("Network error");
        var context = new ErrorContext
        {
            OperationName = "ApiCall",
            ServiceName = "ExternalApi"
        };

        // Act
        var result = await _errorHandler.HandleErrorAsync(exception, context);

        // Assert
        result.Should().NotBeNull();
        result.IsHandled.Should().BeTrue();
        result.Strategy.Should().Be(RecoveryStrategy.Retry);
        result.ShouldRetry.Should().BeTrue();
    }

    [Fact]
    public void ShouldRetry_WithRetriableException_ShouldReturnTrue()
    {
        // Arrange
        var exception = new MarketDataException("Retriable error", isRetriable: true);

        // Act
        var shouldRetry = _errorHandler.ShouldRetry(exception, 1);

        // Assert
        shouldRetry.Should().BeTrue();
    }

    [Fact]
    public void ShouldRetry_WithMaxAttemptsReached_ShouldReturnFalse()
    {
        // Arrange
        var exception = new MarketDataException("Retriable error", isRetriable: true);

        // Act
        var shouldRetry = _errorHandler.ShouldRetry(exception, _options.MaxRetryAttempts);

        // Assert
        shouldRetry.Should().BeFalse();
    }

    [Fact]
    public void CalculateRetryDelay_ShouldUseExponentialBackoff()
    {
        // Arrange
        var exception = new HttpRequestException("Test error");

        // Act
        var delay1 = _errorHandler.CalculateRetryDelay(exception, 1);
        var delay2 = _errorHandler.CalculateRetryDelay(exception, 2);
        var delay3 = _errorHandler.CalculateRetryDelay(exception, 3);

        // Assert
        delay1.Should().Be(TimeSpan.FromMilliseconds(_options.BaseDelayMs));
        delay2.Should().Be(TimeSpan.FromMilliseconds(_options.BaseDelayMs * 2));
        delay3.Should().Be(TimeSpan.FromMilliseconds(_options.BaseDelayMs * 4));
    }

    [Fact]
    public void CalculateRetryDelay_WithSuggestedDelay_ShouldUseSuggestedDelay()
    {
        // Arrange
        var suggestedDelay = TimeSpan.FromSeconds(5);
        var exception = new MarketDataException("Test error", isRetriable: true, suggestedRetryDelay: suggestedDelay);

        // Act
        var delay = _errorHandler.CalculateRetryDelay(exception, 1);

        // Assert
        delay.Should().Be(suggestedDelay);
    }

    [Fact]
    public void CalculateRetryDelay_ShouldRespectMaxDelay()
    {
        // Arrange
        var exception = new HttpRequestException("Test error");

        // Act
        var delay = _errorHandler.CalculateRetryDelay(exception, 10); // Large attempt count

        // Assert
        delay.Should().BeLessOrEqualTo(TimeSpan.FromMilliseconds(_options.MaxDelayMs));
    }

    [Fact]
    public async Task RegisterHandler_ShouldUseCustomHandler()
    {
        // Arrange
        var customHandlerCalled = false;
        _errorHandler.RegisterHandler<ArgumentException>((ex, ctx) =>
        {
            customHandlerCalled = true;
            return Task.FromResult(ErrorHandlingResult.Skip("Custom handler"));
        });

        var exception = new ArgumentException("Test argument error");
        var context = new ErrorContext { OperationName = "Test", ServiceName = "Test" };

        // Act
        var result = await _errorHandler.HandleErrorAsync(exception, context);

        // Assert
        customHandlerCalled.Should().BeTrue();
        result.Strategy.Should().Be(RecoveryStrategy.Skip);
        result.RecoveryMessage.Should().Be("Custom handler");
    }

    [Fact]
    public async Task RegisterCategoryHandler_ShouldUseCustomCategoryHandler()
    {
        // Arrange
        var customHandlerCalled = false;
        _errorHandler.RegisterCategoryHandler(ErrorCategory.MarketData, (ex, ctx) =>
        {
            customHandlerCalled = true;
            return Task.FromResult(ErrorHandlingResult.Fallback("Custom category handler"));
        });

        var exception = new MarketDataException("Test market data error");
        var context = new ErrorContext { OperationName = "Test", ServiceName = "Test" };

        // Act
        var result = await _errorHandler.HandleErrorAsync(exception, context);

        // Assert
        customHandlerCalled.Should().BeTrue();
        result.Strategy.Should().Be(RecoveryStrategy.Fallback);
        result.RecoveryMessage.Should().Be("Custom category handler");
    }

    [Fact]
    public async Task LogErrorAsync_ShouldLogWithCorrectLevel()
    {
        // Arrange
        var exception = new RiskManagementException("Critical risk error", "TestRule", isRetriable: false);
        var context = new ErrorContext
        {
            OperationName = "CalculateRisk",
            ServiceName = "RiskManager"
        };

        // Act
        await _errorHandler.LogErrorAsync(exception, context, "Additional context");

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Critical,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Additional context")),
                exception,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}

public sealed class CircuitBreakerServiceTests
{
    private readonly Mock<ILogger<CircuitBreakerService>> _mockLogger;
    private readonly CircuitBreakerOptions _options;
    private readonly CircuitBreakerService _circuitBreakerService;

    public CircuitBreakerServiceTests()
    {
        _mockLogger = new Mock<ILogger<CircuitBreakerService>>();
        _options = new CircuitBreakerOptions
        {
            FailureThreshold = 3,
            OpenTimeout = TimeSpan.FromSeconds(1),
            SuccessThreshold = 2
        };
        _circuitBreakerService = new CircuitBreakerService(_mockLogger.Object, Options.Create(_options));
    }

    [Fact]
    public async Task ExecuteAsync_WithSuccessfulOperation_ShouldReturnResult()
    {
        // Arrange
        const string serviceName = "TestService";
        const string expectedResult = "Success";

        // Act
        var result = await _circuitBreakerService.ExecuteAsync(
            serviceName,
            () => Task.FromResult(expectedResult));

        // Assert
        result.Should().Be(expectedResult);
        _circuitBreakerService.IsCircuitOpen(serviceName).Should().BeFalse();
    }

    [Fact]
    public async Task ExecuteAsync_WithConsecutiveFailures_ShouldOpenCircuit()
    {
        // Arrange
        const string serviceName = "TestService";
        var failureCount = 0;

        // Act & Assert
        for (int i = 0; i < _options.FailureThreshold; i++)
        {
            try
            {
                await _circuitBreakerService.ExecuteAsync(
                    serviceName,
                    () => throw new InvalidOperationException($"Failure {++failureCount}"));
            }
            catch (InvalidOperationException)
            {
                // Expected
            }
        }

        // Circuit should now be open
        _circuitBreakerService.IsCircuitOpen(serviceName).Should().BeTrue();
    }

    [Fact]
    public async Task ExecuteAsync_WithOpenCircuit_ShouldUseFallback()
    {
        // Arrange
        const string serviceName = "TestService";
        const string fallbackResult = "Fallback";

        // First, open the circuit
        for (int i = 0; i < _options.FailureThreshold; i++)
        {
            try
            {
                await _circuitBreakerService.ExecuteAsync(
                    serviceName,
                    () => throw new InvalidOperationException("Failure"));
            }
            catch (InvalidOperationException)
            {
                // Expected
            }
        }

        // Act
        var result = await _circuitBreakerService.ExecuteAsync(
            serviceName,
            () => Task.FromResult("Should not execute"),
            () => Task.FromResult(fallbackResult));

        // Assert
        result.Should().Be(fallbackResult);
    }

    [Fact]
    public async Task ExecuteAsync_WithOpenCircuitAndNoFallback_ShouldThrowException()
    {
        // Arrange
        const string serviceName = "TestService";

        // First, open the circuit
        for (int i = 0; i < _options.FailureThreshold; i++)
        {
            try
            {
                await _circuitBreakerService.ExecuteAsync(
                    serviceName,
                    () => throw new InvalidOperationException("Failure"));
            }
            catch (InvalidOperationException)
            {
                // Expected
            }
        }

        // Act & Assert
        await Assert.ThrowsAsync<ExternalApiException>(() =>
            _circuitBreakerService.ExecuteAsync(
                serviceName,
                () => Task.FromResult("Should not execute")));
    }

    [Fact]
    public void GetCircuitState_ShouldReturnCorrectInformation()
    {
        // Arrange
        const string serviceName = "TestService";

        // Act
        var state = _circuitBreakerService.GetCircuitState(serviceName);

        // Assert
        state.Should().NotBeNull();
        state.ServiceName.Should().Be(serviceName);
        state.State.Should().Be(CircuitBreakerState.Closed);
        state.FailureCount.Should().Be(0);
    }

    [Fact]
    public void OpenCircuit_ShouldOpenCircuitManually()
    {
        // Arrange
        const string serviceName = "TestService";

        // Act
        _circuitBreakerService.OpenCircuit(serviceName, "Manual test");

        // Assert
        _circuitBreakerService.IsCircuitOpen(serviceName).Should().BeTrue();
        var state = _circuitBreakerService.GetCircuitState(serviceName);
        state.State.Should().Be(CircuitBreakerState.Open);
    }

    [Fact]
    public void CloseCircuit_ShouldCloseCircuitManually()
    {
        // Arrange
        const string serviceName = "TestService";
        _circuitBreakerService.OpenCircuit(serviceName, "Test");

        // Act
        _circuitBreakerService.CloseCircuit(serviceName);

        // Assert
        _circuitBreakerService.IsCircuitOpen(serviceName).Should().BeFalse();
        var state = _circuitBreakerService.GetCircuitState(serviceName);
        state.State.Should().Be(CircuitBreakerState.Closed);
    }

    [Fact]
    public async Task ResetCircuit_ShouldResetAllCounters()
    {
        // Arrange
        const string serviceName = "TestService";

        // Generate some failures
        for (int i = 0; i < 2; i++)
        {
            try
            {
                await _circuitBreakerService.ExecuteAsync(
                    serviceName,
                    () => throw new InvalidOperationException("Failure"));
            }
            catch
            {
                // Expected
            }
        }

        // Act
        _circuitBreakerService.ResetCircuit(serviceName);

        // Assert
        var state = _circuitBreakerService.GetCircuitState(serviceName);
        state.FailureCount.Should().Be(0);
        state.SuccessCount.Should().Be(0);
        state.State.Should().Be(CircuitBreakerState.Closed);
    }
}
