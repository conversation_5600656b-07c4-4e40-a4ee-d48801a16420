using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Globalization;

namespace SmaTrendFollower.MachineLearning.ModelTraining;

/// <summary>
/// Console trainer for market regime classification using FastForest multiclass classification.
/// Trains a model to classify market regimes into 4 categories: Sideways, TrendingUp, TrendingDown, Panic.
/// </summary>
public static class TrainRegimeClassifier
{
    private static readonly ILogger Logger = LoggerFactory.Create(builder => 
        builder.AddConsole().SetMinimumLevel(LogLevel.Information))
        .CreateLogger(typeof(TrainRegimeClassifier));

    /// <summary>
    /// Training row for regime classification
    /// </summary>
    public class RegimeTrainingRow
    {
        [LoadColumn(0)]
        public string Date { get; set; } = string.Empty;

        [LoadColumn(1)]
        public float SPX_Ret { get; set; }

        [LoadColumn(2)]
        public float VIX_Change { get; set; }

        [LoadColumn(3)]
        public float VIX_Level { get; set; }

        [LoadColumn(4)]
        public float Breadth_Score { get; set; }

        [LoadColumn(5)]
        public uint RegimeLabel { get; set; }
    }

    /// <summary>
    /// Prediction output for regime classification
    /// </summary>
    public class RegimePrediction
    {
        [ColumnName("PredictedLabel")]
        public uint PredictedLabel { get; set; }

        [ColumnName("Score")]
        public float[] Score { get; set; } = Array.Empty<float>();
    }

    /// <summary>
    /// Main entry point for regime classifier training
    /// </summary>
    // Commented out to avoid multiple entry point warnings
    // public static async Task<int> Main(string[] args)
    {
        try
        {
            var csvPath = args.Length > 0 ? args[0] : "Model/regime.csv";
            var modelPath = args.Length > 1 ? args[1] : "Model/regime_model.zip";
            
            Logger.LogInformation("Training regime classifier...");
            Logger.LogInformation("CSV Path: {CsvPath}", csvPath);
            Logger.LogInformation("Model Output Path: {ModelPath}", modelPath);

            if (!File.Exists(csvPath))
            {
                Logger.LogError("Training data file not found: {CsvPath}", csvPath);
                return 1;
            }

            var result = await TrainModelAsync(csvPath, modelPath);
            
            if (result.Success)
            {
                Logger.LogInformation("Regime classification model trained successfully!");
                Logger.LogInformation("Accuracy: {Accuracy:P2}", result.Accuracy);
                Logger.LogInformation("Macro F1: {MacroF1:F3}", result.MacroF1);
                Logger.LogInformation("Model saved to: {ModelPath}", modelPath);
                return 0;
            }
            else
            {
                Logger.LogError("Training failed: {Error}", result.ErrorMessage);
                return 1;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Unhandled exception during training");
            return 1;
        }
    }

    /// <summary>
    /// Trains the regime classification model using FastForest
    /// </summary>
    public static async Task<RegimeTrainingResult> TrainModelAsync(string csvPath, string modelPath)
    {
        try
        {
            var mlContext = new MLContext(seed: 42);
            
            // Load and validate data
            Logger.LogInformation("Loading training data from {CsvPath}...", csvPath);
            var dataView = mlContext.Data.LoadFromTextFile<RegimeTrainingRow>(
                csvPath, 
                hasHeader: true, 
                separatorChar: ',');

            var dataCount = dataView.GetRowCount() ?? 0;
            Logger.LogInformation("Loaded {DataCount} training samples", dataCount);

            if (dataCount < 50)
            {
                return new RegimeTrainingResult(false, "Insufficient training data (minimum 50 samples required)",
                    string.Empty, 0, 0, 0, 0, 0);
            }

            // Split data for training and testing
            var trainTestSplit = mlContext.Data.TrainTestSplit(dataView, testFraction: 0.2, seed: 42);
            var trainData = trainTestSplit.TrainSet;
            var testData = trainTestSplit.TestSet;

            // Build training pipeline
            var pipeline = mlContext.Transforms.Conversion.MapValueToKey("Label", "RegimeLabel")
                .Append(mlContext.Transforms.Concatenate("Features",
                    nameof(RegimeTrainingRow.SPX_Ret),
                    nameof(RegimeTrainingRow.VIX_Level),
                    nameof(RegimeTrainingRow.VIX_Change),
                    nameof(RegimeTrainingRow.Breadth_Score)))
                .Append(mlContext.MulticlassClassification.Trainers
                    .LightGbm(numberOfLeaves: 32, numberOfIterations: 150))
                .Append(mlContext.Transforms.Conversion.MapKeyToValue("PredictedLabel"));

            // Train the model
            Logger.LogInformation("Training FastForest model with 150 trees and 32 leaves...");
            var model = pipeline.Fit(trainData);

            // Evaluate the model
            var predictions = model.Transform(testData);
            var metrics = mlContext.MulticlassClassification.Evaluate(predictions);

            Logger.LogInformation("Model evaluation completed:");
            Logger.LogInformation("  Accuracy: {Accuracy:P2}", metrics.MacroAccuracy);
            Logger.LogInformation("  Macro F1: {MacroF1:F3}", metrics.MacroAccuracy); // Using MacroAccuracy as proxy
            Logger.LogInformation("  Log Loss: {LogLoss:F3}", metrics.LogLoss);

            // Create model directory if it doesn't exist
            var modelDir = Path.GetDirectoryName(modelPath);
            if (!string.IsNullOrEmpty(modelDir) && !Directory.Exists(modelDir))
            {
                Directory.CreateDirectory(modelDir);
            }

            // Save the model
            mlContext.Model.Save(model, dataView.Schema, modelPath);
            Logger.LogInformation("Model saved to {ModelPath}", modelPath);

            // Log feature importance (if available)
            await LogFeatureImportanceAsync(mlContext, model, trainData);

            return new RegimeTrainingResult(
                true, 
                "Training completed successfully", 
                modelPath,
                metrics.MacroAccuracy,
                metrics.MacroAccuracy, // Using as proxy for F1
                metrics.LogLoss,
                (int)dataCount,
                (int)(trainData.GetRowCount() ?? 0)
            );
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error training regime classification model");
            return new RegimeTrainingResult(false, ex.Message, string.Empty, 0, 0, 0, 0, 0);
        }
    }

    private static async Task LogFeatureImportanceAsync(MLContext mlContext, ITransformer model, IDataView trainData)
    {
        try
        {
            // Create a prediction engine for feature importance analysis
            var predictionEngine = mlContext.Model.CreatePredictionEngine<RegimeTrainingRow, RegimePrediction>(model);
            
            // Test with sample data to verify model works
            var testSample = new RegimeTrainingRow
            {
                SPX_Ret = 0.01f,
                VIX_Level = 20.0f,
                VIX_Change = -0.05f,
                Breadth_Score = 0.6f
            };

            var prediction = predictionEngine.Predict(testSample);
            Logger.LogInformation("Sample prediction - Regime: {Regime}, Confidence: {Confidence:P1}", 
                (MarketRegime)prediction.PredictedLabel, prediction.Score.Max());
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Could not analyze feature importance");
        }
    }
}

/// <summary>
/// Result of regime classification training
/// </summary>
public record RegimeTrainingResult(
    bool Success,
    string ErrorMessage,
    string ModelPath,
    double Accuracy,
    double MacroF1,
    double LogLoss,
    int TotalSamples,
    int TrainingSamples
);
